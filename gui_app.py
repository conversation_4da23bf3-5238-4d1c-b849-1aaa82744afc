import tkinter as tk
from tkinter import filedialog, messagebox, scrolledtext, ttk
from dotenv import load_dotenv, set_key
from pathlib import Path
import os
import dental_core as core
import logging
from datetime import datetime

# 创建日志目录
log_dir = Path('logs')
log_dir.mkdir(exist_ok=True)
timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
log_file = log_dir / f'dental_export_{timestamp}.log'

# 配置日志系统
logger = logging.getLogger("DentalApp")
logger.setLevel(logging.INFO)

file_handler = logging.FileHandler(log_file, encoding='utf-8')
console_handler = logging.StreamHandler()

formatter = logging.Formatter('%(asctime)s [%(levelname)s] %(message)s')
file_handler.setFormatter(formatter)
console_handler.setFormatter(formatter)

logger.addHandler(file_handler)
logger.addHandler(console_handler)

# 加载环境变量
env_file = ".env.c-prod"
load_dotenv(dotenv_path=env_file)

# 全局变量控制任务状态
stop_flag = {"stop": False}

# GUI 主窗口
root = tk.Tk()
root.title("牙模切割线处理工具")
root.geometry("1000x700")

log_text = scrolledtext.ScrolledText(root, height=20, wrap=tk.WORD)
log_text.pack(padx=10, pady=10, fill=tk.BOTH, expand=True)

progress_bar = ttk.Progressbar(root, orient="horizontal", length=800, mode="determinate")
progress_bar.pack(pady=10)

status_label = tk.Label(root, text="等待开始...", relief=tk.SUNKEN)
status_label.pack(side=tk.BOTTOM, fill=tk.X)

# 自定义日志处理器
class TextHandler(logging.Handler):
    def __init__(self, text_widget):
        super().__init__()
        self.text_widget = text_widget

    def emit(self, record):
        msg = self.format(record)
        def append():
            self.text_widget.configure(state='normal')
            self.text_widget.insert('end', msg + '\n')
            self.text_widget.configure(state='disabled')
            self.text_widget.see('end')
        self.text_widget.after(0, append)

# 绑定日志到 GUI
text_handler = TextHandler(log_text)
text_formatter = logging.Formatter('%(asctime)s [%(levelname)s] %(message)s')
text_handler.setFormatter(text_formatter)
logger.addHandler(text_handler)
core.setup_logger(logger)

# 更新进度条
def update_progress(current, total):
    progress_bar["maximum"] = total
    progress_bar["value"] = current
    percent = (current / total) * 100
    status_label.config(text=f"处理进度: {current}/{total} ({percent:.1f}%)")

# 完成回调
def task_done(success, total, stopped=False):
    if stopped:
        status_label.config(text="任务已手动中止")
        messagebox.showinfo("提示", "任务已被用户中止。")
    else:
        status_label.config(text=f"完成: {success}/{total} 成功")
        messagebox.showinfo("完成", f"成功处理 {success}/{total} 个病例。")

# 启动任务
def start_task():
    stop_flag["stop"] = False
    input_path = input_entry.get()
    core.generate_ai_retainer(input_path, stop_flag, update_progress, task_done)

# 停止任务
def stop_task():
    stop_flag["stop"] = True
    status_label.config(text="正在中止任务...")

# 输入路径选择
def select_input_path():
    path = filedialog.askdirectory()
    if path:
        input_entry.delete(0, tk.END)
        input_entry.insert(0, path)

# 配置输入框
input_frame = tk.Frame(root)
input_frame.pack(pady=5, fill=tk.X)

tk.Label(input_frame, text="输入路径:").pack(side=tk.LEFT, padx=5)
input_entry = tk.Entry(input_frame, width=60)
input_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
tk.Button(input_frame, text="浏览", command=select_input_path).pack(side=tk.LEFT, padx=5)
tk.Button(input_frame, text="开始处理", command=start_task).pack(side=tk.LEFT, padx=5)
tk.Button(input_frame, text="停止任务", command=stop_task).pack(side=tk.LEFT, padx=5)

# 运行主循环
if __name__ == "__main__":
    root.mainloop()