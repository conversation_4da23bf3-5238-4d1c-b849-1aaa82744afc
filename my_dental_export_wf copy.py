#!/usr/bin/env python
# coding: utf-8

# In[21]:


import os
import json
import time
import urllib
import base64
import requests
import logging
from dotenv import load_dotenv, find_dotenv
from typing import TypedDict
import shutil 
import threading
import tkinter as tk
from tkinter import messagebox
from datetime import datetime






# 配置日志
def setup_logging():
    # 创建logs目录（如果不存在）
    if not os.path.exists('logs'):
        os.makedirs('logs')
    
    # 生成日志文件名（使用当前时间）
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_filename = f'logs/dental_export_{timestamp}.log'

    
    # 配置日志格式
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s [%(levelname)s] %(message)s',
        handlers=[
            logging.FileHandler(log_filename, encoding='utf-8'),  # .log文件
            logging.StreamHandler()  # 同时输出到控制台
        ]
    )
    return logging.getLogger(__name__)

# 初始化日志
logger = setup_logging()




version = 'v1.0.2'
# change the env here
env = '.env.c-prod' 
load_dotenv(dotenv_path=env)
env_name = os.getenv('env_name')
program_name = os.getenv('program_name')
print('env_name: ',env_name)

#choho_token和user_id是调用工作流时候的身份标识
choho_token = os.getenv('CHOHO_TOKEN')
user_id = os.getenv('USER_ID')

input_upper_folder_last_name = os.getenv('input_upper_folder_last_name')
input_lower_folder_last_name = os.getenv('input_lower_folder_last_name')


upper_folder_last_name = os.getenv('upper_folder_last_name')
lower_folder_last_name = os.getenv('lower_folder_last_name')



param = os.getenv('param')
input_path = os.getenv('INPUT_PATH')

print('input_upper_folder_last_name: ', input_upper_folder_last_name)
print('input_lower_folder_last_name: ', input_lower_folder_last_name)

print('upper_folder_last_name: ', upper_folder_last_name)
print('lower_folder_last_name: ', lower_folder_last_name)


print('param: ', param)
print('---------------------------------------------------')
print('\n')




# 使用logger记录配置信息
logger.info('Configuration:')
logger.info(f'env_name: {env_name}')
logger.info(f'Input Upper Folder Last Name: {input_upper_folder_last_name}')
logger.info(f'Input Lower Folder Last Name: {input_lower_folder_last_name}')
logger.info(f'Upper Folder Last Name: {upper_folder_last_name}')
logger.info(f'Lower Folder Last Name: {lower_folder_last_name}')
logger.info(f'Parameter: {param}')
logger.info(f'Input Path: {input_path}')
logger.info('---------------------------------------------------')
logger.info('')




def readInputFolderName(path):
    directory_path = path
    all_items = os.listdir(directory_path)
    folders = [item for item in all_items if os.path.isdir(os.path.join(directory_path, item))]
    cases = []

    for folder in folders:
        if folder.isdigit():
            case_id = int(folder)
            folder_path = os.path.join(directory_path, folder)
            items = os.listdir(folder_path)

            upper_files = []
            lower_files = []

            # Classify files by jaw type and param index
            for item in items:
                if item.endswith('.stl'):
                    base_name = item[:-4]  # remove .stl
                    
                    # Detect jaw type
                    if 'U' in base_name:
                        jaw_type = 'Upper'
                        is_complete = '-C' in base_name or 'C' in base_name  # Check for -C or C suffix
                        is_temp = '-E' in base_name or 'E' in base_name  # Check for -E or E suffix
                        param_index = 1 if is_complete else 0
                        output_name = f"{case_id}U{'_C' if is_complete else '_E' if is_temp else ''}"
                        upper_files.append((item, param_index, output_name))
                        
                    elif 'L' in base_name:
                        jaw_type = 'Lower'
                        is_complete = '-C' in base_name or 'C' in base_name
                        is_temp = '-E' in base_name or 'E' in base_name  # Check for -E or E suffix
                        param_index = 1 if is_complete else 0
                        output_name = f"{case_id}L{'_C' if is_complete else '_E' if is_temp else ''}"
                        lower_files.append((item, param_index, output_name))

            # Build case data
            for file_name, param_index, output_name in upper_files:
                file_path = os.path.join(folder_path, file_name)
                case_data = {
                    "case_id": case_id,
                    "upper": {
                        "name": output_name,
                        "nameTBar": f"{case_id}U",
                        "jaw_type": "Upper",
                        "path": file_path,
                        "param_index": param_index
                    }
                }
                cases.append(case_data)

            for file_name, param_index, output_name in lower_files:
                file_path = os.path.join(folder_path, file_name)
                case_data = {
                    "case_id": case_id,
                    "lower": {
                        "name": output_name,
                        "nameTBar": f"{case_id}L",
                        "jaw_type": "Lower",
                        "path": file_path,
                        "param_index": param_index
                    }
                }
                cases.append(case_data)

        else:
            print(f"Error: Invalid folder name {folder}")

    return cases



# In[24]:


#upload函数负责将指定路径的mesh文件（一般是本地）上传到朝厚云，并返回对应的urn
#如果mesh文件的格式不是stl文件，请修改postfix为对应的后缀
def upload(data_path):
    with open(data_path, "rb") as f:
        input_data = f.read()

    resp = requests.get(f'https://file-server.dev.chohotech.com/scratch/APIClient/moderndental-api/upload_url?postfix=stl',
                        headers={
                            "X-ZH-TOKEN": '6pCxxGyD4z35EC2aCsPjPakfQEJf9P1t24SYmYKdNmlHy9WQbuUcguMmG255qf'})
    resp.raise_for_status()

    upload_url = json.loads(resp.text)
    resp = requests.put(upload_url, input_data)
    resp.raise_for_status()

    path = '/'.join(urllib.parse.urlparse(upload_url).path.lstrip('/').split('/')[3:])
    urn = f"urn:zhfile:o:s:APIClient:moderndental-api:{path}"
    
    print(urn)
    logger.info(urn)
    
    return urn


# ### 以下为牙模导出与切割线相关工作流的输入输出定义
# inputs:
# -    mesh: mesh #输入的口扫模型
# -    jaw_type: enum[str]{"Upper", "Lower"} #指定的口扫模型是上颌还是下颌
# -    deciduous: optional[bool] #是否有乳牙，默认选True
# -    export_params: #导出牙模与切割线使用的参数
# -      $struct:
# -        inner_with: float 内壁厚度
# -        text: str #打印在T-bar的文字
# -        curve_bias_distance: float #切割线距离龈牙线的最小距离
# -        need_waved_curve: bool #是否是波浪状切割线，True时是波浪状；False时为平直状 
# -        waved_weight: float #波浪线的起伏程度，取值在(0,1)之间，当need_waved_curve为False时无作用
# -        bias_lingual_anterior_teeth: bool #前牙3-3区域的舌侧切割线是否上移
# -        hypodontia_dist_thr: float #如果两颗牙齿之间的距离超过了该值，这两颗牙齿之间的切割线变为平直状
# outputs:
# -    dental_mesh: mesh
# -    cutline: binary
# -    laser_marker: binary

# In[25]:


def step_1(upload_path, my_jaw_type, fileIndexName, nameTBar):
    class ModernDentalParams(TypedDict):
        inner_width: float
        text: str
        curve_bias_distance: float
        need_waved_curve: bool
        waved_weight: float
        bias_lingual_anterior_teeth: bool
        hypodontia_dist_thr: float

    scan_mesh = {"type": "stl", "data": upload(upload_path)}
    if env_name is not None and '.env.e-' in env_name:
        upload_path = upload_path.replace('.stl', '-E.stl') 
        unprocessed_mesh = {"type": "stl", "data": upload(upload_path)}
    else:
        unprocessed_mesh = scan_mesh
    
    jaw_type = my_jaw_type
    # 对应文档中第一类切割线
    params0 = ModernDentalParams(text=nameTBar, curve_bias_distance=0.9,
                                 need_waved_curve=True,
                                 waved_weight=0.9,
                                 bias_lingual_anterior_teeth=False,
                                 hypodontia_dist_thr=1.0,
                                 inner_width=2.5)
    # 对应文档中第二类切割线
    params1 = ModernDentalParams(text=nameTBar, curve_bias_distance=0.9,
                                 need_waved_curve=True,
                                 waved_weight=0.9,
                                 bias_lingual_anterior_teeth=True,
                                 hypodontia_dist_thr=1.0,
                                 inner_width=2.5)
    # 对应文档中第三类切割线
    params2 = ModernDentalParams(text=nameTBar, curve_bias_distance=1.5,
                                 need_waved_curve=False,
                                 waved_weight=0.9,
                                 bias_lingual_anterior_teeth=True,
                                 hypodontia_dist_thr=1.0,
                                 inner_width=2.5)

    # 对于直接指定保存文件，目前暂不支持，需要下载内容后再保存到指定位置
    target_dental_mesh_path = f'{fileIndexName}.model.stl'
    target_laser_marker_path = f'{fileIndexName}.las.txt'
    target_cutline_path = f'{fileIndexName}.pts.txt'
    return scan_mesh, unprocessed_mesh, jaw_type, params0, params1, params2, target_dental_mesh_path, target_laser_marker_path, target_cutline_path


# In[26]:


def step_2(scan_mesh, unprocessed_mesh,jaw_type, param):
    #以下是调用工作流的请求，请求成功后，run_id变量中存储了该次调用的工作流id
    #输出mesh的格式在output_config字段中进行指定，为了演示的方便，这里使用了stl格式，推荐使用drcp的格式，可以极大降低传输数据的大小
    #在notification字段中，设置了回调请求的地址，请将相应的url替换为您的相应地址
    url = f"https://zhmle-workflow-api.dev.chohotech.com/workflow/run"
    if env_name is not None and '.env.e' in env_name:
        payload = json.dumps({
            "spec_group": "api-customized",
            "spec_name": "wf-modern-cut",
            "spec_version": "1.0-snapshot",
            "user_group": "APIClient",
            "user_id": "moderndental-api",
            "input_data": {
                "mesh":unprocessed_mesh,
                "unprocessed_mesh":scan_mesh,
                "jaw_type": jaw_type,
                "jaw_type": jaw_type,
                "deciduous": True,
                "export_params": param,
            },
            "output_config":{
                    "dental_mesh": {"type": "stl","output-location": "bytes"},
                    "cutline": {"output-location": "bytes"},
                    "laser_marker":{"output-location": "bytes"},
                },
            "notification": [{
                    "url": "https://cn.bing.com/",
                    "failure": True,
                    "success": True,
                    "version": "v1"
                }]
        })
    else:
        payload = json.dumps({
            "spec_group": "api-customized",
            "spec_name": "wf-modern-cut",
            "spec_version": "1.0-snapshot",
            "user_group": "APIClient",
            "user_id": user_id,
            "input_data": {
                "mesh":scan_mesh,
                "jaw_type": jaw_type,
                "jaw_type": jaw_type,
                "deciduous": True,
                "export_params": param,
            },
            "output_config":{
                    "dental_mesh": {"type": "stl","output-location": "bytes"},
                    "cutline": {"output-location": "bytes"},
                    "laser_marker":{"output-location": "bytes"},
                },
            "notification": [{
                    "url": "https://cn.bing.com/",
                    "failure": True,
                    "success": True,
                    "version": "v1"
                }]
        })
    
    

    headers = {
        'Content-Type': 'application/json',
        "X-ZH-TOKEN": "6pCxxGyD4z35EC2aCsPjPakfQEJf9P1t24SYmYKdNmlHy9WQbuUcguMmG255qf"
    }
    # post request
    response = requests.request("POST", url, headers=headers, data=payload)
    response.raise_for_status()
    create_result = response.json()
    run_id = create_result['run_id']
    print(run_id)
    logger.info(f"Workflow run ID: {run_id}")
    # 由于本地开发的限制，此处我们无法直接使用回调函数对结果进行处理，因此使用轮询，查询工作流是否完成。
    # 设置超时为1000秒
    timeout = 1500
    start_time = time.time()

    headers = {
        'Content-Type': 'application/json',
        "X-ZH-TOKEN": "6pCxxGyD4z35EC2aCsPjPakfQEJf9P1t24SYmYKdNmlHy9WQbuUcguMmG255qf"
    }

    while True:
        if time.time() - start_time > timeout:
            raise TimeoutError('任务超时失败')
        time.sleep(1)
        print('.', end='', flush=True)

        response = requests.request("GET", f"https://zhmle-workflow-api.dev.chohotech.com/workflow/run/{run_id}",
                                    headers=headers)
        result = response.json()

        if result['completed'] or result['failed']:
            break

    print('\n')
    print(result)
        
    logger.info("Workflow completed")
    logger.info(f"Result: {json.dumps(result, indent=2)}")
    
    return headers, run_id


# In[27]:


def step_3(target_cutline_path, target_dental_mesh_path, target_laser_marker_path, run_id, headers):
    # 在轮询查询完毕/接收到回调函数之后，确认工作流正常执行完毕后，根据工作流id请求响应数据，并保存到本地。
    response = requests.request("GET", f"https://zhmle-workflow-api.dev.chohotech.com/workflow/data/{run_id}",
                                headers=headers)
    result = response.content
    json_content = json.loads(response.content)
    cutline_binary = base64.b64decode(json_content['cutline'])
    dental_mesh_binary = base64.b64decode(json_content['dental_mesh']['data'])
    laser_marker_binary = base64.b64decode(json_content['laser_marker'])

    with open(target_cutline_path, "wb") as binary_file:    # Write bytes to file
        binary_file.write(cutline_binary)
        binary_file.close()

    with open(target_dental_mesh_path, "wb") as binary_file:    # Write bytes to file
        binary_file.write(dental_mesh_binary)
        binary_file.close()

    with open(target_laser_marker_path, "wb") as binary_file:    # Write bytes to file
        binary_file.write(laser_marker_binary)
        binary_file.close()


# # 请参照 https://cloud.docs.chohotech.com/#/introduction/call_workflow 详细了解关于工作流调用和回调函数相关的内容。

# # laser marker的格式目前如下，由于角度定义的不明确，暂时以方向向量代替，该方向向量从牙根指向牙冠
# # 目前，在确定打标位置时，我们会尝试沿着牙弓曲线的方向和唇舌侧的方向进行放置，目前输出文件中没有该信息输出
# - -21.60045 -1.80116 #logo xy坐标
# - 0.08945 0.04963 0.99475 #logo 所在牙齿的方向向量
# - 11.36780 #logo z坐标
# - 21.53644 -1.03028 #text0 xy 坐标
# - -0.15966 0.00952 0.98713 #text0 所在牙齿的方向向量
# - 10.49662 # text0 z坐标
# - 23.57414 -0.29258 #text1 xy 坐标
# - -0.15966 0.00952 0.98713 #text1 所在牙齿的方向向量
# - 12.22820 # text1 z坐标
# - 172963U # 输入的text信息

# In[28]:



def process_item(item, update_path, case_id, item_path, jaw_type, name, nameTBar):
    def get_params(value, params0, params1, params2):
        params = {
            0: params0,
            1: params1,
            2: params2
        }
        print(f'param number: {int(value)}')
        return params[int(value)]
     
    # if it is c , the name will be changed to elminate the -c by Kent Mai
    if name.endswith('-C') or name.endswith('-c'):
        name = name[:-2]
    # if it is e , the name will be changed to elminate the -e by Kent Mai
    if name.endswith('-e') or name.endswith('-e'):
        name = name[:-2]
        
    # if one file fail, that will keep moving to run next file
    try:
        print('current building - ', name )
        logger.info('current building - %s', name )
        
        # step_1
        scan_mesh, unprocessed_mesh, jaw_type, params0, params1, params2, target_dental_mesh_path, target_laser_marker_path, target_cutline_path = step_1(item_path, jaw_type, name, nameTBar)
        
        print('process_item - param: ', get_params(param, params0, params1, params2))
        logger.info('process_item - param: ', get_params(param, params0, params1, params2))
        
        # step_2
        headers, run_id = step_2(scan_mesh, unprocessed_mesh, jaw_type, get_params(param, params0, params1, params2))
        # step_3
        step_3(target_cutline_path, target_dental_mesh_path, target_laser_marker_path, run_id, headers)
        

        destination = f'{update_path}/{case_id}/{name}'
        os.makedirs(destination)
        shutil.move(target_cutline_path, destination) 
        shutil.move(target_dental_mesh_path, destination) 
        shutil.move(target_laser_marker_path, destination) 
        
        
        print('Finish - ' + name )
        print('\n')
        
        logger.info('Finish - ' + name )
        logger.info('\n')
        
        return 1
    except Exception as e:
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")  # Format the current timestamp
            show_error_dialog(f'[{timestamp}] case_id: {case_id} \n, name: {name} \n, error_msg: {e}')
            error_log_path = f'{update_path}/{case_id}/error_log.txt'
            with open(error_log_path, "a") as error_log:
                error_log.write(f'[{timestamp}] case_id: {case_id} \n name: {name} \n {e} \n\n')
                
            print('\n')
            print(f'Error: generateAiRetainer - {e}')
            print('\n')
            
            logger.info('\n')
            logger.info(f'Error: generateAiRetainer - {e}')
            logger.info('\n')
            
            return 0
        
    



def generate_ai_retainer(update_path):
        result = readInputFolderName(update_path)
        # print(json.dumps(result, indent=4))
        logger.info(json.dumps(result, indent=4))
        
        for item in result:
            upper = item.get('upper', {})
            lower = item.get('lower', {})
            
            count_upper = 1 if upper.get('path') is not None else 0
            count_lower = 1 if lower.get('path') is not None else 0
            count = count_upper + count_lower
        # print(f'sub_total_count: {count}')
        logger.info(f'sub_total_count: {count}')
            
        case_id = item['case_id']
            
        if upper.get('path'):
            count -= process_item(item, update_path=update_path, case_id = case_id, item_path = item['upper']['path'], jaw_type = item['upper']['jaw_type'], name = item['upper']['name'], nameTBar=item['upper']['nameTBar'])
                
        if lower.get('path'):
            count -= process_item(item, update_path, case_id, item['lower']['path'], item['lower']['jaw_type'], item['lower']['name'], nameTBar=item['lower']['nameTBar'])
                
        if count == 0:
            os.rename(f'{update_path}/{case_id}', f'{update_path}/{case_id}_OK')
        else:
            os.rename(f'{update_path}/{case_id}', f'{update_path}/{case_id}_NG')
        
        
        
def show_error_dialog(text):
    # Create a new thread for the dialog to avoid blocking the main thread
    def dialog_thread():
        root = tk.Tk()
        root.withdraw()  # Hide the root window

        # Create a Toplevel window for the error dialog
        dialog = tk.Toplevel(root)
        dialog.title("Error")
        # dialog.geometry("300x100")

        # Add a label to display the error message
        label = tk.Label(dialog, text= text, fg="red", font=("Arial", 9), wraplength=280)  # Set wrap length to 280 pixels)
        label.pack(padx=10, pady=10)
        
        # Update the dialog size dynamically based on the label's content
        dialog.update_idletasks()  # Ensure all widgets are rendered
        dialog.geometry(f"{dialog.winfo_reqwidth()}x{dialog.winfo_reqheight()}")

        # Keep the dialog running without blocking
        root.mainloop()

    threading.Thread(target=dialog_thread, daemon=True).start()
    

def run_job(path):
    # my local test in window, don't delete
  
    generate_ai_retainer(path)

    # generate_ai_retainer('Z:/Finished_designs_4F/Retainer')
    
    # generate_ai_retainer('//172.18.86.6/dpdata/Finished_designs_4F/Retainer')
    


if __name__ == "__main__":
    
    input_paths = [
        'input',
        'input-c',
        'input-e',
        # Add more directories here
    ]
    
   
    logger.info(f'{program_name} program: {version}')

    while True:
        # print('Session Start')
        logger.info('Session Start')
        
        # Create a thread for each directory
        threads = []
        for path in input_paths:
            job_thread = threading.Thread(target=run_job, args=(path,))
            job_thread.start()
            threads.append(job_thread)

        # Wait for all threads to complete
        for t in threads:
            t.join()

        print('Session Completed')
        logger.info('Session Completed')

        # Countdown timer 
        total_seconds = 1 * 5    
        while total_seconds > 0:
            hours, remainder = divmod(total_seconds, 3600)
            minutes, seconds = divmod(remainder, 60)
            print(f"\rTime until next run: {hours:02}:{minutes:02}:{seconds:02}", end='', flush=True)
            logger.info(f"Time until next run: {hours:02}:{minutes:02}:{seconds:02}")
            time.sleep(1)
            total_seconds -= 1
            
        
        
        print('\n')

