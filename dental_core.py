# dental_core.py

import os
import json
import time
import base64
import shutil
import requests
from datetime import datetime
from typing import TypedDict, List, Dict, Optional
import logging
from urllib.parse import urlparse   
logger = None  # 将由 GUI 初始化时传入

class ModernDentalParams(TypedDict):
    inner_width: float
    text: str
    curve_bias_distance: float
    need_waved_curve: bool
    waved_weight: float
    bias_lingual_anterior_teeth: bool
    hypodontia_dist_thr: float

def setup_logger(custom_logger):
    global logger
    logger = custom_logger

def read_input_folders(path: str) -> List[Dict]:
    directory_path = path
    folders = [item for item in os.listdir(directory_path) if os.path.isdir(os.path.join(directory_path, item))]
    cases = []

    input_upper_suffix = os.getenv("input_upper_folder_last_name")
    input_lower_suffix = os.getenv("input_lower_folder_last_name")
    upper_output_suffix = os.getenv("upper_folder_last_name")
    lower_output_suffix = os.getenv("lower_folder_last_name")

    for folder in folders:
        if not folder.isdigit():
            continue
        case_id = int(folder)
        folder_path = os.path.join(directory_path, folder)
        items = os.listdir(folder_path)

        upper_path = None
        lower_path = None
        upper_name = f"{case_id}{upper_output_suffix}"
        lower_name = f"{case_id}{lower_output_suffix}"

        upper_is_exist = os.path.exists(os.path.join(directory_path, str(case_id), upper_name))
        lower_is_exist = os.path.exists(os.path.join(directory_path, str(case_id), lower_name))

        if upper_is_exist or lower_is_exist:
            logger.info(f"Skipping already processed case {case_id}")
            continue

        for item in items:
            if item.endswith(f"{input_upper_suffix}.stl"):
                upper_path = os.path.join(folder_path, item)
            elif item.endswith(f"{input_lower_suffix}.stl"):
                lower_path = os.path.join(folder_path, item)

        case_data = {"case_id": case_id}
        if upper_path:
            case_data["upper"] = {
                "name": upper_name,
                "nameTBar": f"{case_id}U",
                "jaw_type": "Upper",
                "path": upper_path
            }
        if lower_path:
            case_data["lower"] = {
                "name": lower_name,
                "nameTBar": f"{case_id}L",
                "jaw_type": "Lower",
                "path": lower_path
            }

        if not upper_path and not lower_path:
            logger.error(f"Missing both Upper and Lower files for case {case_id}")
        else:
            cases.append(case_data)

    return cases

def upload(data_path: str, user_id: str, token: str) -> str:
    with open(data_path, "rb") as f:
        input_data = f.read()
    url = f'https://api.chohotech.com/file-server/scratch/APIClient/{user_id}/upload_url?postfix=stl'
    resp = requests.get(url, headers={"X-ZH-TOKEN": token})
    resp.raise_for_status()
    upload_url = resp.json()

    resp = requests.put(upload_url, input_data)
    resp.raise_for_status()

    parsed = urlparse(upload_url)
    path = '/'.join(parsed.path.lstrip('/').split('/')[3:])
    urn = f"urn:zhfile:o:s:APIClient:{user_id}:{path}"
    logger.info(f"Uploaded: {urn}")
    return urn

def step_1(data_path: str, jaw_type: str, name: str, nameTBar: str, user_id: str, token: str):
    scan_mesh = {"type": "stl", "data": upload(data_path, user_id, token)}
    unprocessed_mesh = scan_mesh
    env_name = os.getenv('env_name', '')

    if '.env.e-' in env_name:
        data_path_e = data_path.replace('.stl', '-E.stl')
        if os.path.exists(data_path_e):
            unprocessed_mesh = {"type": "stl", "data": upload(data_path_e, user_id, token)}

    params0 = ModernDentalParams(
        inner_width=2.5, text=nameTBar, curve_bias_distance=0.9,
        need_waved_curve=True, waved_weight=0.9,
        bias_lingual_anterior_teeth=False, hypodontia_dist_thr=1.0
    )
    params1 = ModernDentalParams(
        inner_width=2.5, text=nameTBar, curve_bias_distance=0.9,
        need_waved_curve=True, waved_weight=0.9,
        bias_lingual_anterior_teeth=True, hypodontia_dist_thr=1.0
    )
    params2 = ModernDentalParams(
        inner_width=2.5, text=nameTBar, curve_bias_distance=1.5,
        need_waved_curve=False, waved_weight=0.9,
        bias_lingual_anterior_teeth=True, hypodontia_dist_thr=1.0
    )

    target_dental_mesh_path = f'{name}.model.stl'
    target_laser_marker_path = f'{name}.las.txt'
    target_cutline_path = f'{name}.pts.txt'

    return scan_mesh, unprocessed_mesh, jaw_type, params0, params1, params2, \
           target_dental_mesh_path, target_laser_marker_path, target_cutline_path

def step_2(scan_mesh, unprocessed_mesh, jaw_type, param, user_id, token):
    url = "https://api.chohotech.com/zhmle-workflow-api/workflow/run"
    payload = json.dumps({
        "spec_group": "api-customized",
        "spec_name": "wf-modern-cut",
        "spec_version": "1.0-snapshot",
        "user_group": "APIClient",
        "user_id": user_id,
        "input_data": {
            "mesh": scan_mesh,
            "unprocessed_mesh": unprocessed_mesh if 'unprocessed_mesh' in locals() else scan_mesh,
            "jaw_type": jaw_type,
            "deciduous": True,
            "export_params": param
        },
        "output_config": {
            "dental_mesh": {"type": "stl", "output-location": "bytes"},
            "cutline": {"output-location": "bytes"},
            "laser_marker": {"output-location": "bytes"}
        },
        "notification": [{
            "url": "https://cn.bing.com/ ",
            "failure": True,
            "success": True,
            "version": "v1"
        }]
    })
    headers = {'Content-Type': 'application/json', "X-ZH-TOKEN": token}

    response = requests.post(url, headers=headers, data=payload)
    response.raise_for_status()
    run_id = response.json()['run_id']
    logger.info(f"Started workflow: {run_id}")

    timeout = 1500
    start_time = time.time()
    while True: 
        if time.time() - start_time > timeout:
            raise TimeoutError("任务超时失败")
        time.sleep(1)
        resp = requests.get(f"https://api.chohotech.com/zhmle-workflow-api/workflow/run/{run_id}", headers=headers)
        result = resp.json()
        if result.get('completed') or result.get('failed'):
            break
    logger.info(f"Workflow completed: {run_id}")
    return headers, run_id

def step_3(cutline_path, mesh_path, laser_path, run_id, headers):
    resp = requests.get(f"https://api.chohotech.com/zhmle-workflow-api/workflow/data/{run_id}", headers=headers)
    content = resp.json()
    with open(cutline_path, 'wb') as f:
        f.write(base64.b64decode(content['cutline']))
    with open(mesh_path, 'wb') as f:
        f.write(base64.b64decode(content['dental_mesh']['data']))
    with open(laser_path, 'wb') as f:
        f.write(base64.b64decode(content['laser_marker']))

def process_case_item(item_info, input_path, stop_flag):
    case_id = item_info["case_id"]
    results = []

    def get_param(value, p0, p1, p2):
        return {0: p0, 1: p1, 2: p2}[int(value)]

    def do_process(jaw_info):
        if stop_flag["stop"]:
            return 0
        name = jaw_info["name"]
        path = jaw_info["path"]
        jaw_type = jaw_info["jaw_type"]
        nameTBar = jaw_info["nameTBar"]

        try:
            logger.info(f"[{case_id}] Processing {jaw_type}: {name}")
            scan_mesh, unprocessed_mesh, jtype, p0, p1, p2, cut, laser, mesh = step_1(
                path, jaw_type, name, nameTBar, os.getenv("USER_ID"), os.getenv("CHOHO_TOKEN")
            )
            selected_param = get_param(int(os.getenv("param", "0")), p0, p1, p2)
            headers, run_id = step_2(scan_mesh, unprocessed_mesh, jtype, selected_param, os.getenv("USER_ID"), os.getenv("CHOHO_TOKEN"))
            step_3(cut, mesh, laser, run_id, headers)

            dest_dir = os.path.join(input_path, str(case_id), name)
            os.makedirs(dest_dir, exist_ok=True)
            for f in [cut, mesh, laser]:
                shutil.move(f, os.path.join(dest_dir, os.path.basename(f)))
            logger.info(f"[{case_id}] Finished {name}")
            return 1
        except Exception as e:
            logger.error(f"[{case_id}] Error processing {name}: {e}")
            return 0

    if "upper" in item_info:
        results.append(do_process(item_info["upper"]))
    if "lower" in item_info:
        results.append(do_process(item_info["lower"]))
    return sum(results)

def generate_ai_retainer(update_path, stop_flag, progress_callback, done_callback):
    from threading import Thread

    def worker():
        cases = read_input_folders(update_path)
        total = len(cases)
        success_count = 0
        for idx, case in enumerate(cases):
            if stop_flag["stop"]:
                logger.info("任务已中止")
                done_callback(success_count, total, stopped=True)
                return
            res = process_case_item(case, update_path, stop_flag)
            success_count += res
            progress_callback(idx + 1, total)
        done_callback(success_count, total)

    Thread(target=worker, daemon=True).start()