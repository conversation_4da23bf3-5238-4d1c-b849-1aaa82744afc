env config in keeper
<br/>
<br/>
document:
https://cloud.docs.chohotech.com/#/introduction/call_workflow
<br/>

Don't use jupyter for development. Now using my_dental_export_wf.py. 
The reason is that the changes in git, .py is much clearer than .ipynb.
If the following using jupyter, you can ignore it. 


Remember to install Conda and ceate an independent python environment (Not global one) for this program

<br/>
Change the INPUT_PATH in the .env for your development device and create one development env for your own. Please follow the .env.example


<br/>
if window cannot use .venv, solution as below

https://jason06286.medium.com/powershell-%E8%A7%A3%E6%B1%BA-ps1-%E6%AA%94%E6%A1%88%E7%84%A1%E6%B3%95%E8%BC%89%E5%85%A5-%E5%9B%A0%E7%82%BA%E9%80%99%E5%80%8B%E7%B3%BB%E7%B5%B1%E4%B8%8A%E5%B7%B2%E5%81%9C%E7%94%A8%E6%8C%87%E4%BB%A4%E7%A2%BC%E5%9F%B7%E8%A1%8C-%E5%95%8F%E9%A1%8C-5bddce485e6d



```
pip install pyinstaller
```


After activate python venv and run
```
python .\my_dental_export_wf.py
```


<br/>


*When build, remember to change the env in .py


Prod for retainer
```
pyinstaller --add-data=".env.retainer-prod;." --onefile --name retainer.py my_dental_export_wf.py
```


Prod for c-retainer
```
pyinstaller --add-data=".env.c-prod;." --onefile --name c.py my_dental_export_wf.py
```

Dev for E-retainer
```
pyinstaller --add-data=".env.e-dev;." --onefile --name E-retainer.py my_dental_export_wf.py
```
<br/>



```
pyinstaller -F my_dental_export_wf.py

pyinstaller --onefile my_dental_export_wf.py 

pyinstaller --add-data=".env;." --onefile  my_dental_export_wf.py

pyinstaller --add-data=".env.retainer-dev;." --onefile  my_dental_export_wf.py

pyinstaller --add-data=".env.retainer-prod;." --onefile  my_dental_export_wf.py

pyinstaller --add-data=".env.retainer-dev;." --onefile --name retainer.py my_dental_export_wf.py
pyinstaller --add-data=".env.retainer-prod;." --onefile --name retainer.py my_dental_export_wf.py
```


R-C

```
jupyter nbconvert --to script my_dental_export_wf_c.ipynb
```

```
pyinstaller -F my_dental_export_wf_c.py

pyinstaller --onefile my_dental_export_wf_c.py 

pyinstaller --add-data=".env;." --onefile  my_dental_export_wf.py

pyinstaller --add-data=".env.c-dev;." --onefile  my_dental_export_wf.py
pyinstaller --add-data=".env.c-prod;." --onefile  my_dental_export_wf.py

pyinstaller --add-data=".env.c-dev;." --onefile --name c.py my_dental_export_wf.py
pyinstaller --add-data=".env.c-prod;." --onefile --name c.py my_dental_export_wf.py


<!-- ENV_FILE=".env.c-dev"; pyinstaller --add-data=".env.c-dev;." --onefile --name c.py my_dental_export_wf.py -->

```

