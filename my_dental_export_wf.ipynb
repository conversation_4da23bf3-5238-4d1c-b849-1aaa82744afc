{"cells": [{"cell_type": "code", "execution_count": 21, "id": "81dc5c55-7069-424b-bd46-7a9660623165", "metadata": {}, "outputs": [], "source": ["import os\n", "import json\n", "import time\n", "import urllib\n", "import base64\n", "import requests\n", "from dotenv import load_dotenv\n", "from typing import TypedDict\n", "import shutil \n", "import threading"]}, {"cell_type": "code", "execution_count": 22, "id": "7ad68fec-e3eb-4b5d-a856-d1b5c10f4727", "metadata": {}, "outputs": [], "source": ["load_dotenv(dotenv_path='.env')\n", "\n", "#choho_token和user_id是调用工作流时候的身份标识\n", "choho_token = os.getenv('CHOHO_TOKEN')\n", "user_id = os.getenv('USER_ID')\n"]}, {"cell_type": "code", "execution_count": 23, "id": "b3b6f674", "metadata": {}, "outputs": [], "source": ["def readInputFolderName(path):\n", "    # Define the directory path\n", "    directory_path = path\n", "\n", "    # List all items in the directory\n", "    all_items = os.listdir(directory_path)\n", "\n", "    # Filter out only directories\n", "    folders = [item for item in all_items if os.path.isdir(\n", "        os.path.join(directory_path, item))]\n", "\n", "    cases = []\n", "\n", "    for folder in folders:\n", "        if folder.isdigit():\n", "            case_id = int(folder)\n", "            folder_path = f'{directory_path}/{folder}'\n", "            items = os.listdir(folder_path)\n", "            upper_path = None\n", "            lower_path = None\n", "            upper_name = None\n", "            lower_name = None\n", "            \n", "            upper_is_exist = os.path.exists(f'{directory_path}/{case_id}/{case_id}U')\n", "            lower_is_exist = os.path.exists(f'{directory_path}/{case_id}/{case_id}L')\n", "            \n", "            \n", "            for item in items:\n", "                if 'U.stl' in item and upper_is_exist == False:\n", "                    upper_path = f'{folder_path}/{item}'\n", "                    upper_name = f'{case_id}U'\n", "                elif 'L.stl' in item and lower_is_exist == False:\n", "                    lower_path = f'{folder_path}/{item}'\n", "                    lower_name = f'{case_id}L'\n", "\n", "            if upper_path and lower_path:\n", "                case_data = {\n", "                    \"case_id\": case_id,\n", "                    \"upper\": {\n", "                        \"name\": upper_name,\n", "                        \"jaw_type\": \"Upper\",\n", "                        \"path\": upper_path\n", "                    },\n", "                    \"lower\": {\n", "                        \"name\": lower_name,\n", "                        \"jaw_type\": \"Lower\",\n", "                        \"path\": lower_path\n", "                    }\n", "                }\n", "                cases.append(case_data)\n", "            else:\n", "                if(upper_path and not lower_path):\n", "                    case_data = {\n", "                        \"case_id\": case_id,\n", "                        \"upper\": {\n", "                            \"name\": upper_name,\n", "                            \"jaw_type\": \"Upper\",\n", "                            \"path\": upper_path\n", "                        },\n", "                    }\n", "                    cases.append(case_data)\n", "                \n", "                if(lower_path and not upper_path):\n", "                    case_data = {\n", "                        \"case_id\": case_id,\n", "                        \"lower\": {\n", "                            \"name\": lower_name,\n", "                            \"jaw_type\": \"Lower\",\n", "                            \"path\": lower_path\n", "                        }\n", "                    }\n", "                    cases.append(case_data)\n", "                    \n", "                \n", "                if(upper_is_exist):\n", "                    print(f'{case_id}U already exists, stop processing this file')\n", "                if(lower_is_exist):\n", "                    print(f'{case_id}L already exists, stop processing this file')\n", "                     \n", "                if(not lower_path and not lower_is_exist):\n", "                    print(f'{case_id}L.stl does not exist')\n", "                    \n", "                if(not upper_path and not upper_is_exist):\n", "                    print(f'{case_id}U.stl does not exist')\n", "                     \n", "                if(not lower_is_exist and not upper_is_exist):\n", "                     print(f'Error: Upper or Lower jaw missing for case {case_id}')\n", "        else:\n", "            print(f'Error: Invalid folder name {folder}')\n", "\n", "    return cases\n", "\n", "\n", "# result = readInputFolderName('input')\n", "# print(json.dumps(result, indent=4))"]}, {"cell_type": "code", "execution_count": 24, "id": "ad908e53-3737-4dd8-bd25-04456f9cc709", "metadata": {}, "outputs": [], "source": ["#upload函数负责将指定路径的mesh文件（一般是本地）上传到朝厚云，并返回对应的urn\n", "#如果mesh文件的格式不是stl文件，请修改postfix为对应的后缀\n", "def upload(data_path):\n", "    with open(data_path, \"rb\") as f:\n", "        input_data = f.read()\n", "\n", "    resp = requests.get(f'https://file-server.dev.chohotech.com/scratch/APIClient/{user_id}/upload_url?postfix=stl',\n", "                        headers={\n", "                            \"X-ZH-TOKEN\": choho_token})\n", "    resp.raise_for_status()\n", "\n", "    upload_url = json.loads(resp.text)\n", "    resp = requests.put(upload_url, input_data)\n", "    resp.raise_for_status()\n", "\n", "    path = '/'.join(urllib.parse.urlparse(upload_url).path.lstrip('/').split('/')[3:])\n", "    urn = f\"urn:zhfile:o:s:APIClient:{user_id}:{path}\"\n", "\n", "    print(urn)\n", "\n", "    return urn"]}, {"cell_type": "markdown", "id": "2ef0a19d-3077-4e2f-b7fd-a5701a3530fd", "metadata": {}, "source": ["### 以下为牙模导出与切割线相关工作流的输入输出定义\n", "inputs:\n", "-    mesh: mesh #输入的口扫模型\n", "-    jaw_type: enum[str]{\"Upper\", \"Lower\"} #指定的口扫模型是上颌还是下颌\n", "-    deciduous: optional[bool] #是否有乳牙，默认选True\n", "-    export_params: #导出牙模与切割线使用的参数\n", "-      $struct:\n", "-        inner_with: float 内壁厚度\n", "-        text: str #打印在T-bar的文字\n", "-        curve_bias_distance: float #切割线距离龈牙线的最小距离\n", "-        need_waved_curve: bool #是否是波浪状切割线，True时是波浪状；False时为平直状 \n", "-        waved_weight: float #波浪线的起伏程度，取值在(0,1)之间，当need_waved_curve为False时无作用\n", "-        bias_lingual_anterior_teeth: bool #前牙3-3区域的舌侧切割线是否上移\n", "-        hypodontia_dist_thr: float #如果两颗牙齿之间的距离超过了该值，这两颗牙齿之间的切割线变为平直状\n", "outputs:\n", "-    dental_mesh: mesh\n", "-    cutline: binary\n", "-    laser_marker: binary"]}, {"cell_type": "code", "execution_count": 25, "id": "f115bd75", "metadata": {}, "outputs": [], "source": ["def step_1(upload_path, my_jaw_type, fileIndexName):\n", "    # from typing import TypedDict\n", "    class ModernDentalParams(TypedDict):\n", "        inner_width: float\n", "        text: str\n", "        curve_bias_distance: float\n", "        need_waved_curve: bool\n", "        waved_weight: float\n", "        bias_lingual_anterior_teeth: bool\n", "        hypodontia_dist_thr: float\n", "\n", "    scan_mesh = {\"type\": \"stl\", \"data\": upload(upload_path)}\n", "    jaw_type = my_jaw_type\n", "    # 对应文档中第一类切割线\n", "    params0 = ModernDentalParams(text=fileIndexName, curve_bias_distance=0.9,\n", "                                 need_waved_curve=True,\n", "                                 waved_weight=0.9,\n", "                                 bias_lingual_anterior_teeth=False,\n", "                                 hypodontia_dist_thr=1.0,\n", "                                 inner_width=2.5)\n", "    # 对应文档中第二类切割线\n", "    params1 = ModernDentalParams(text=fileIndexName, curve_bias_distance=0.9,\n", "                                 need_waved_curve=True,\n", "                                 waved_weight=0.9,\n", "                                 bias_lingual_anterior_teeth=True,\n", "                                 hypodontia_dist_thr=1.0,\n", "                                 inner_width=2.5)\n", "    # 对应文档中第三类切割线\n", "    params2 = ModernDentalParams(text=fileIndexName, curve_bias_distance=1.5,\n", "                                 need_waved_curve=False,\n", "                                 waved_weight=0.9,\n", "                                 bias_lingual_anterior_teeth=True,\n", "                                 hypodontia_dist_thr=1.0,\n", "                                 inner_width=2.5)\n", "\n", "    # 对于直接指定保存文件，目前暂不支持，需要下载内容后再保存到指定位置\n", "    target_dental_mesh_path = f'{fileIndexName}.model.stl'\n", "    target_laser_marker_path = f'{fileIndexName}.las.txt'\n", "    target_cutline_path = f'{fileIndexName}.pts.txt'\n", "    return scan_mesh, jaw_type, params0, params1, params2, target_dental_mesh_path, target_laser_marker_path, target_cutline_path"]}, {"cell_type": "code", "execution_count": 26, "id": "4d1ca914", "metadata": {}, "outputs": [], "source": ["def step_2(scan_mesh, jaw_type, param):\n", "    #以下是调用工作流的请求，请求成功后，run_id变量中存储了该次调用的工作流id\n", "    #输出mesh的格式在output_config字段中进行指定，为了演示的方便，这里使用了stl格式，推荐使用drcp的格式，可以极大降低传输数据的大小\n", "    #在notification字段中，设置了回调请求的地址，请将相应的url替换为您的相应地址\n", "    url = \"https://zhmle-workflow-api.dev.chohotech.com/workflow/run\"\n", "    payload = json.dumps({\n", "        \"spec_group\": \"api-customized\",\n", "        \"spec_name\": \"wf-modern-cut\",\n", "        \"spec_version\": \"1.0-snapshot\",\n", "        \"user_group\": \"APIClient\",\n", "        \"user_id\": user_id,\n", "        \"input_data\": {\n", "            \"mesh\":scan_mesh,\n", "            \"jaw_type\": jaw_type,\n", "            \"deciduous\": True,\n", "            \"export_params\": param,\n", "        },\n", "        \"output_config\":{\n", "                \"dental_mesh\": {\"type\": \"stl\",\"output-location\": \"bytes\"},\n", "                \"cutline\": {\"output-location\": \"bytes\"},\n", "                \"laser_marker\":{\"output-location\": \"bytes\"},\n", "            },\n", "        \"notification\": [{\n", "                \"url\": \"https://cn.bing.com/\",\n", "                \"failure\": True,\n", "                \"success\": True,\n", "                \"version\": \"v1\"\n", "            }]\n", "    })\n", "\n", "    headers = {\n", "        'Content-Type': 'application/json',\n", "        \"X-ZH-TOKEN\": choho_token\n", "    }\n", "    # post request\n", "    response = requests.request(\"POST\", url, headers=headers, data=payload)\n", "    response.raise_for_status()\n", "    create_result = response.json()\n", "    run_id = create_result['run_id']\n", "    print(run_id)\n", "    \n", "    # 由于本地开发的限制，此处我们无法直接使用回调函数对结果进行处理，因此使用轮询，查询工作流是否完成。\n", "    # 设置超时为1000秒\n", "    timeout = 1000\n", "    start_time = time.time()\n", "\n", "    headers = {\n", "        'Content-Type': 'application/json',\n", "        \"X-ZH-TOKEN\": choho_token\n", "    }\n", "\n", "    while True:\n", "        if time.time() - start_time > timeout:\n", "            raise TimeoutError('任务超时失败')\n", "        time.sleep(1)\n", "        print('.', end='', flush=True)\n", "\n", "        response = requests.request(\"GET\", f\"https://zhmle-workflow-api.dev.chohotech.com/workflow/run/{run_id}\",\n", "                                    headers=headers)\n", "        result = response.json()\n", "\n", "        if result['completed'] or result['failed']:\n", "            break\n", "\n", "    print(result)\n", "        \n", "    return headers, run_id"]}, {"cell_type": "code", "execution_count": 27, "id": "8fac69ec", "metadata": {}, "outputs": [], "source": ["def step_3(target_cutline_path, target_dental_mesh_path, target_laser_marker_path, run_id, headers):\n", "    # 在轮询查询完毕/接收到回调函数之后，确认工作流正常执行完毕后，根据工作流id请求响应数据，并保存到本地。\n", "    response = requests.request(\"GET\", f\"https://zhmle-workflow-api.dev.chohotech.com/workflow/data/{run_id}\",\n", "                                headers=headers)\n", "    result = response.content\n", "    json_content = json.loads(response.content)\n", "    cutline_binary = base64.b64decode(json_content['cutline'])\n", "    dental_mesh_binary = base64.b64decode(json_content['dental_mesh']['data'])\n", "    laser_marker_binary = base64.b64decode(json_content['laser_marker'])\n", "\n", "    with open(target_cutline_path, \"wb\") as binary_file:    # Write bytes to file\n", "        binary_file.write(cutline_binary)\n", "        binary_file.close()\n", "\n", "    with open(target_dental_mesh_path, \"wb\") as binary_file:    # Write bytes to file\n", "        binary_file.write(dental_mesh_binary)\n", "        binary_file.close()\n", "\n", "    with open(target_laser_marker_path, \"wb\") as binary_file:    # Write bytes to file\n", "        binary_file.write(laser_marker_binary)\n", "        binary_file.close()"]}, {"cell_type": "markdown", "id": "73fb1a0d-3365-4b65-8b24-1c63afcc9724", "metadata": {}, "source": ["# 请参照 https://cloud.docs.chohotech.com/#/introduction/call_workflow 详细了解关于工作流调用和回调函数相关的内容。"]}, {"cell_type": "markdown", "id": "bcb4e1a0-139a-42bf-9e5d-92d2ecd0fd76", "metadata": {}, "source": ["# laser marker的格式目前如下，由于角度定义的不明确，暂时以方向向量代替，该方向向量从牙根指向牙冠\n", "# 目前，在确定打标位置时，我们会尝试沿着牙弓曲线的方向和唇舌侧的方向进行放置，目前输出文件中没有该信息输出\n", "- -21.60045 -1.80116 #logo xy坐标\n", "- 0.08945 0.04963 0.99475 #logo 所在牙齿的方向向量\n", "- 11.36780 #logo z坐标\n", "- 21.53644 -1.03028 #text0 xy 坐标\n", "- -0.15966 0.00952 0.98713 #text0 所在牙齿的方向向量\n", "- 10.49662 # text0 z坐标\n", "- 23.57414 -0.29258 #text1 xy 坐标\n", "- -0.15966 0.00952 0.98713 #text1 所在牙齿的方向向量\n", "- 12.22820 # text1 z坐标\n", "- 172963U # 输入的text信息"]}, {"cell_type": "code", "execution_count": 28, "id": "6f1ed827", "metadata": {}, "outputs": [], "source": ["def process_item(item, update_path, case_id, item_path, jaw_type, name):\n", "    print('current runnning - ' + name )\n", "    # step_1\n", "    scan_mesh, jaw_type, params0, params1, params2, target_dental_mesh_path, target_laser_marker_path, target_cutline_path = step_1(item_path, jaw_type, name)\n", "    # step_2\n", "    headers, run_id = step_2(scan_mesh, jaw_type, params0)\n", "    # step_3\n", "    step_3(target_cutline_path, target_dental_mesh_path, target_laser_marker_path, run_id, headers)\n", "    \n", "\n", "    destination = f'{update_path}/{case_id}/{name}'\n", "    os.makedirs(destination)\n", "    shutil.move(target_cutline_path, destination) \n", "    shutil.move(target_dental_mesh_path, destination) \n", "    shutil.move(target_laser_marker_path, destination) \n", "    \n", "    print('Finish - ' + name )\n", "    print('\\n')\n", "        \n", "    \n", "\n", "\n", "\n", "def generateAiRetainer(update_path):\n", "    try:\n", "        result = readInputFolderName(update_path)\n", "        print(json.dumps(result, indent=4))\n", "        \n", "        for item in result:\n", "            upper = item.get('upper', {})\n", "            lower = item.get('lower', {})\n", "            if upper.get('path'):\n", "                process_item(item, update_path=update_path, case_id = item['case_id'], item_path = item['upper']['path'], jaw_type = item['upper']['jaw_type'], name = item['upper']['name'])\n", "                \n", "            if lower.get('path'):\n", "                process_item(item, update_path, item['case_id'], item['lower']['path'], item['lower']['jaw_type'], item['lower']['name'])\n", "                \n", "        \n", "        \n", "        \n", "    except Exception as e:\n", "        print(f'Error: generateAiRetainer - {e}')\n", "    \n"]}, {"cell_type": "code", "execution_count": 29, "id": "6edf3ed5", "metadata": {}, "outputs": [], "source": ["def run_job():\n", "    # my local test in window, don't delete\n", "    # generateAiRetainer('C:/Users/<USER>/Documents/Ai-Retainer/input')\n", "    \n", "    generateAiRetainer('Z:/Finished_designs_4F/Retainer')\n", "    \n", "    # generateAiRetainer('//172.18.86.6/dpdata/Finished_designs_4F/Retainer')\n", "    "]}, {"cell_type": "code", "execution_count": 30, "id": "5b9e89aa", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Session Start\n", "20101U already exists, stop processing this file\n", "20101L already exists, stop processing this file\n", "20102U already exists, stop processing this file\n", "20102L already exists, stop processing this file\n", "20103U already exists, stop processing this file\n", "20103L already exists, stop processing this file\n", "29744L already exists, stop processing this file\n", "29744U.stl does not exist\n", "[]\n", "Session Completed\n", "Time until next run: 00:00:41"]}, {"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[30], line 18\u001b[0m\n\u001b[0;32m     16\u001b[0m     minutes, seconds \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mdivmod\u001b[39m(remainder, \u001b[38;5;241m60\u001b[39m)\n\u001b[0;32m     17\u001b[0m     \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;130;01m\\r\u001b[39;00m\u001b[38;5;124mTime until next run: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mhours\u001b[38;5;132;01m:\u001b[39;00m\u001b[38;5;124m02\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m:\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mminutes\u001b[38;5;132;01m:\u001b[39;00m\u001b[38;5;124m02\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m:\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mseconds\u001b[38;5;132;01m:\u001b[39;00m\u001b[38;5;124m02\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m, end\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m'\u001b[39m, flush\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mTrue\u001b[39;00m)\n\u001b[1;32m---> 18\u001b[0m     \u001b[43mtime\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msleep\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m1\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[0;32m     19\u001b[0m     total_seconds \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m=\u001b[39m \u001b[38;5;241m1\u001b[39m\n\u001b[0;32m     21\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m'\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;124m'\u001b[39m)\n", "\u001b[1;31mKeyboardInterrupt\u001b[0m: "]}], "source": ["if __name__ == \"__main__\":\n", "    print('Retainer program: v1.0.1')\n", "    while True:\n", "        print('Session Start')\n", "        # Create a thread for the job\n", "        job_thread = threading.Thread(target=run_job)\n", "        job_thread.start()\n", "        \n", "        # Wait for the job to complete\n", "        job_thread.join()\n", "        print('Session Completed')\n", "        \n", "        # Countdown timer for 3 hours\n", "        total_seconds = 1 * 60    # 3 hours in seconds\n", "        while total_seconds > 0:\n", "            hours, remainder = divmod(total_seconds, 3600)\n", "            minutes, seconds = divmod(remainder, 60)\n", "            print(f\"\\rTime until next run: {hours:02}:{minutes:02}:{seconds:02}\", end='', flush=True)\n", "            time.sleep(1)\n", "            total_seconds -= 1\n", "        \n", "        print('\\n')"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 5}